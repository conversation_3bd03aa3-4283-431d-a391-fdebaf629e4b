import multiprocessing
import os
import json
import datetime
from dataflow.operators.filter import (
    LanguageFilter,
    ColonEndFilter,
    WordNumberFilter,
    BlocklistFilter,
    SentenceNumberFilter,
    LineEndWithEllipsisFilter,
    ContentNullFilter,
    MeanWord<PERSON>ength<PERSON>ilter,
    SymbolWordRatioFilter,
    HtmlEntityFilter,
    IDCardFilter,
    NoPuncFilter,
    SpecialCharacterFilter,
    WatermarkFilter,
    CurlyBracketFilter,
    CapitalWordsFilter,
    LoremIpsumFilter,
    UniqueWordsFilter,
    CharNumberFilter,
    LineStartWithBulletpointFilter,
    LineWithJavascriptFilter,
    PairQualFilter
)
from dataflow.operators.refine import (
    HtmlUrlRemoverRefiner,
    RemoveEmojiRefiner,
    RemoveExtraSpacesRefiner,
    RemoveImageRefsRefiner
)

from dataflow.utils.storage import FileStorage


class CleaningPipeline:
    def __init__(self):
        self.remove_extra_spaces_refiner = RemoveExtraSpacesRefiner()
        self.remove_emoji_refiner = RemoveEmojiRefiner()
        self.html_remove_refiner = HtmlUrlRemoverRefiner()
        self.blocklist_filter = BlocklistFilter()
        self.word_number_filter = WordNumberFilter(min_words=5, max_words=100000)
        self.colon_end_filter = ColonEndFilter()
        self.sentence_number_filter = SentenceNumberFilter(min_sentences=3, max_sentences=7500)
        self.line_end_with_ellipsis_filter = LineEndWithEllipsisFilter(threshold=0.3)
        self.content_null_filter = ContentNullFilter()
        self.mean_word_length_filter = MeanWordLengthFilter(min_length=3, max_length=10)
        self.symbol_word_ratio_filter = SymbolWordRatioFilter(threshold=0.4)
        self.html_entity_filter = HtmlEntityFilter()
        # self.id_card_filter = IDCardFilter(threshold=3)
        self.no_punc_filter = NoPuncFilter(threshold=112)
        self.special_character_filter = SpecialCharacterFilter()
        self.watermark_filter = WatermarkFilter(
            watermarks=['Copyright', 'Watermark', 'Confidential', '版权所有', '保留所有权利'])
        self.curly_bracket_filter = CurlyBracketFilter(threshold=0.025)
        self.capital_words_filter = CapitalWordsFilter(threshold=0.2, use_tokenizer=False)
        self.lorem_ipsum_filter = LoremIpsumFilter(threshold=3e-8)
        self.unique_words_filter = UniqueWordsFilter(threshold=0.1)
        self.char_number_filter = CharNumberFilter(threshold=100)
        self.line_start_with_bulletpoint_filter = LineStartWithBulletpointFilter(threshold=0.9)
        self.line_with_javascript_filter = LineWithJavascriptFilter(threshold=3)
        self.remove_image_ref_refiner = RemoveImageRefsRefiner()

    def forward(self, storage):
        """执行清理管道的前向处理"""
        try:
            self.remove_extra_spaces_refiner.run(
                storage=storage.step(),
                input_key="text"
            )
            self.remove_emoji_refiner.run(
                storage=storage.step(),
                input_key="text"
            )
            self.html_remove_refiner.run(
                storage=storage.step(),
                input_key="text"
            )
            self.blocklist_filter.run(
                storage=storage.step(),
                input_key="text",
            )
            self.colon_end_filter.run(
                storage=storage.step(),
                input_key="text"
            )
            self.sentence_number_filter.run(
                storage=storage.step(),
                input_key="text"
            )
            self.line_end_with_ellipsis_filter.run(
                storage=storage.step(),
                input_key="text"
            )
            self.content_null_filter.run(
                storage=storage.step(),
                input_key="text",
            )
            self.html_entity_filter.run(
                storage=storage.step(),
                input_key="text",
            )
            # self.id_card_filter.run(
            # storage = storage.step(),
            # input_key = "text",
            # )
            self.special_character_filter.run(
            storage = storage.step(),
            input_key = "text",

            )
            self.watermark_filter.run(
            storage = storage.step(),
            input_key = "text",

            )
            self.curly_bracket_filter.run(
            storage = storage.step(),
            input_key = "text",
            )
            self.lorem_ipsum_filter.run(
            storage = storage.step(),
            input_key = "text",
            )
            self.unique_words_filter.run(
            storage = storage.step(),
            input_key = "text",
            )
            self.char_number_filter.run(
            storage = storage.step(),
            input_key = "text",
            )
            self.line_start_with_bulletpoint_filter.run(
            storage = storage.step(),
            input_key = "text",
            )
            self.line_with_javascript_filter.run(
            storage = storage.step(),
            input_key = 'text',
            )
            self.remove_image_ref_refiner.run(
            storage = storage.step(),
            input_key = 'text',
            )
            return True
        except Exception as e:
            print(f"Pipeline处理出错: {e}")
            return False


def process_single_item(args):
    """
    处理单个数据项的工作函数
    这个函数将在独立的进程中运行
    """
    data, time_now, process_id = args

    raw_content = data.get('raw_content')
    if not raw_content:
        print(f"进程 {process_id}: 跳过无效数据: {data}")
        return False, process_id, raw_content

    try:
        print(f"进程 {process_id}: 开始处理 {raw_content}")

        # 为每个进程创建独立的pipeline实例
        pipeline = CleaningPipeline()

        # 提取文件名
        file_name = raw_content.split('/')[-1].split('.')[0]

        # 为每个进程创建独立的storage
        storage = FileStorage(
            first_entry_file_name=raw_content,
            cache_path=f"/home/<USER>/datasets/cache/general_cleaning/longdata/{time_now}",
            file_name_prefix=f"{file_name}_process_{process_id}_step",
            cache_type="jsonl",
        )

        # 执行处理
        success = pipeline.forward(storage)

        if success:
            print(f"进程 {process_id}: 成功处理 {raw_content}")
        else:
            print(f"进程 {process_id}: 处理失败 {raw_content}")

        return success, process_id, raw_content

    except Exception as e:
        print(f"进程 {process_id}: 处理出错 {raw_content}, 错误: {e}")
        return False, process_id, raw_content


def process_batch(batch_args):
    """
    处理一批数据的工作函数
    每个进程处理多个项目以减少进程创建开销
    """
    batch_data, time_now, process_id = batch_args

    print(f"进程 {process_id}: 开始处理批次，包含 {len(batch_data)} 个项目")

    # 为每个进程创建独立的pipeline实例
    pipeline = CleaningPipeline()

    results = []
    success_count = 0

    for i, data in enumerate(batch_data):
        raw_content = data.get('raw_content')
        if not raw_content:
            print(f"进程 {process_id}-{i}: 跳过无效数据")
            results.append((False, process_id, raw_content))
            continue

        try:
            print(f"进程 {process_id}-{i}: 处理 {raw_content}")

            # 提取文件名
            file_name = raw_content.split('/')[-1].split('.')[0]

            # 为每个文件创建独立的storage
            storage = FileStorage(
                first_entry_file_name=raw_content,
                cache_path=f"/home/<USER>/datasets/cache/general_cleaning/longdata/{time_now}",
                file_name_prefix=f"{file_name}_process_{process_id}_item_{i}_step",
                cache_type="jsonl",
            )

            # 执行处理
            success = pipeline.forward(storage)

            if success:
                success_count += 1
                print(f"进程 {process_id}-{i}: 成功处理 {raw_content}")
            else:
                print(f"进程 {process_id}-{i}: 处理失败 {raw_content}")

            results.append((success, process_id, raw_content))

        except Exception as e:
            print(f"进程 {process_id}-{i}: 处理出错 {raw_content}, 错误: {e}")
            results.append((False, process_id, raw_content))

    print(f"进程 {process_id}: 批次处理完成 {success_count}/{len(batch_data)} 成功")
    return results


def main():
    """主函数"""
    # 配置参数
    entry_file_name = "/home/<USER>/datasets/LongData-Corpus/85000.jsonl"
    time_now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

    # 多进程配置
    BATCH_SIZE = 5  # 每批处理5个项目（减少批次大小以平衡负载）
    MAX_WORKERS = min(multiprocessing.cpu_count(), 8)  # 基于CPU核心数确定进程数

    print(f"使用 {MAX_WORKERS} 个进程进行并行处理")

    # 读取数据
    print("读取输入文件...")
    data_items = []
    try:
        with open(entry_file_name, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    data_items.append(data)
                except json.JSONDecodeError as e:
                    print(f"跳过第{line_num}行，JSON解析错误: {e}")
                    continue
    except FileNotFoundError:
        print(f"错误: 找不到文件 {entry_file_name}")
        return
    except Exception as e:
        print(f"读取文件出错: {e}")
        return

    if not data_items:
        print("没有找到有效的数据项")
        return

    print(f"成功读取 {len(data_items)} 个数据项")

    # 分批处理
    batches = [data_items[i:i + BATCH_SIZE] for i in range(0, len(data_items), BATCH_SIZE)]
    print(f"分为 {len(batches)} 批进行处理")

    # 准备进程参数
    batch_args = [(batch, time_now, i + 1) for i, batch in enumerate(batches)]

    # 使用进程池处理
    total_success = 0
    total_processed = 0

    print("开始多进程处理...")

    try:
        with multiprocessing.Pool(processes=MAX_WORKERS) as pool:
            # 提交所有批次任务
            results = pool.map(process_batch, batch_args)

            # 汇总结果
            for batch_results in results:
                for success, process_id, raw_content in batch_results:
                    total_processed += 1
                    if success:
                        total_success += 1

    except Exception as e:
        print(f"多进程处理出错: {e}")

    print(f"\n所有任务完成:")
    print(f"总处理项目: {total_processed}")
    print(f"成功处理: {total_success}")
    print(f"失败项目: {total_processed - total_success}")
    print(f"成功率: {total_success / total_processed * 100:.2f}%" if total_processed > 0 else "0%")


if __name__ == "__main__":
    # 确保多进程在Windows上正常工作
    multiprocessing.set_start_method('spawn', force=True)

    # 运行主程序
    main()