from dataflow.operators.generate import (
    AutoPromptGenerator,
    QAGenerator,
    QAScorer
)

from dataflow.operators.filter import (
    ContentChooser
)

from dataflow.utils.storage import FileStorage
from dataflow.serving import APILLMServing_request
from dataflow.serving import LocalModelLLMServing_vllm
import datetime, json
import logging
import time
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qa_generation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QA_APIPipeline():
    def __init__(self, max_retries=3, retry_delay=5):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # 添加错误统计
        self.error_stats = {
            'api_errors': 0,
            'file_errors': 0,
            'processing_errors': 0
        }

        try:
            self.storage = FileStorage(
                first_entry_file_name="/home/<USER>//datasets/domain_行业数据/CSG行业数据/qa_chunked_text.json",
                cache_path="./cache_qa",
                file_name_prefix="202508071257_dataflow_qa_cache_step",
                cache_type="jsonl",
            )

            # 添加API连接测试
            self.llm_serving = self._init_llm_serving_with_retry()
            
            self.prompt_generator_step1 = AutoPromptGenerator(self.llm_serving)
            self.qa_generator_step2 = QAGenerator(self.llm_serving)
            self.qa_scorer_step3 = QAScorer(self.llm_serving)
            
            logger.info("QA Pipeline 初始化成功")
            
        except Exception as e:
            logger.error(f"Pipeline 初始化失败: {e}")
            raise

    def _init_llm_serving_with_retry(self):
        """带重试的LLM服务初始化"""
        for attempt in range(self.max_retries):
            try:
                llm_serving = APILLMServing_request(
                    api_url="http://*************:8008/v1/chat/completions",
                    model_name="DeepSeek-V3-0324",
                    max_workers=10
                )
                
                # 测试连接
                test_response = llm_serving.test_connection()
                if test_response:
                    logger.info("LLM服务连接成功")
                    return llm_serving
                    
            except Exception as e:
                logger.warning(f"LLM服务初始化失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise

    def _safe_step_execution(self, step_func, step_name, **kwargs):
        """安全执行处理步骤"""
        for attempt in range(self.max_retries):
            try:
                logger.info(f"执行步骤: {step_name} (尝试 {attempt + 1})")
                result = step_func(**kwargs)
                logger.info(f"步骤 {step_name} 执行成功")
                return result
                
            except Exception as e:
                self.error_stats['processing_errors'] += 1
                logger.error(f"步骤 {step_name} 执行失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"步骤 {step_name} 最终失败")
                    raise

    def forward(self):
        """执行完整的QA生成流程"""
        try:
            # 步骤1: 生成提示词
            self._safe_step_execution(
                self.prompt_generator_step1.run,
                "提示词生成",
                storage=self.storage.step(),
                input_key="raw_chunk"
            )

            # 步骤2: 生成QA
            self._safe_step_execution(
                self.qa_generator_step2.run,
                "QA生成",
                storage=self.storage.step(),
                input_key="raw_chunk",
                prompt_key="generated_prompt",
                output_quesion_key="generated_question",
                output_answer_key="generated_answer"
            )

            # 步骤3: QA评分
            self._safe_step_execution(
                self.qa_scorer_step3.run,
                "QA评分",
                storage=self.storage.step(),
                input_question_key="generated_question",
                input_answer_key="generated_answer",
                output_question_quality_key="question_quality_grades",
                output_question_quality_feedback_key="question_quality_feedbacks",
                output_answer_alignment_key="answer_alignment_grades",
                output_answer_alignment_feedback_key="answer_alignment_feedbacks",
                output_answer_verifiability_key="answer_verifiability_grades",
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Pipeline执行失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def process_single_file(self, raw_content, file_name, time_now):
        """处理单个文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(raw_content):
                logger.error(f"文件不存在: {raw_content}")
                self.error_stats['file_errors'] += 1
                return False

            # 检查文件大小
            file_size = os.path.getsize(raw_content)
            if file_size == 0:
                logger.warning(f"文件为空: {raw_content}")
                return False

            logger.info(f"开始处理文件: {raw_content} (大小: {file_size} bytes)")

            # 更新存储配置
            self.storage = FileStorage(
                first_entry_file_name=raw_content,
                cache_path="./cache_qa",
                file_name_prefix=f"{time_now}_{file_name}_qa_aa_step",
                cache_type="jsonl",
            )

            # 执行处理流程
            success = self.forward()
            
            if success:
                logger.info(f"文件处理成功: {raw_content}")
            else:
                logger.error(f"文件处理失败: {raw_content}")
                
            return success

        except Exception as e:
            logger.error(f"处理文件时发生异常 {raw_content}: {e}")
            logger.error(traceback.format_exc())
            self.error_stats['processing_errors'] += 1
            return False

def main():
    """主函数 - 增强错误处理和并发控制"""
    entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/domain_qa_aa.jsonl"
    time_now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    
    # 创建输出目录
    os.makedirs("./cache_qa", exist_ok=True)
    
    try:
        # 初始化pipeline
        pipeline = QA_APIPipeline()
        
        # 读取并验证输入文件
        if not os.path.exists(entry_file_name):
            logger.error(f"入口文件不存在: {entry_file_name}")
            return

        # 读取任务列表
        tasks = []
        with open(entry_file_name, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    raw_content = data.get('chunk_path')
                    
                    if raw_content:
                        file_name = Path(raw_content).stem
                        tasks.append((raw_content, file_name))
                    else:
                        logger.warning(f"第{line_num}行缺少chunk_path字段: {data}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"第{line_num}行JSON解析错误: {e}")
                    continue

        logger.info(f"共找到 {len(tasks)} 个处理任务")

        # 顺序处理（避免API并发限制）
        success_count = 0
        for i, (raw_content, file_name) in enumerate(tasks, 1):
            logger.info(f"处理进度: {i}/{len(tasks)} - {raw_content}")
            
            success = pipeline.process_single_file(raw_content, file_name, time_now)
            if success:
                success_count += 1
            
            # 添加处理间隔，避免API限流
            time.sleep(1)

        # 输出统计信息
        logger.info("=" * 50)
        logger.info("处理完成统计:")
        logger.info(f"总任务数: {len(tasks)}")
        logger.info(f"成功处理: {success_count}")
        logger.info(f"失败数量: {len(tasks) - success_count}")
        logger.info(f"API错误: {pipeline.error_stats['api_errors']}")
        logger.info(f"文件错误: {pipeline.error_stats['file_errors']}")
        logger.info(f"处理错误: {pipeline.error_stats['processing_errors']}")
        logger.info("=" * 50)

    except Exception as e:
        logger.error(f"主程序执行失败: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()