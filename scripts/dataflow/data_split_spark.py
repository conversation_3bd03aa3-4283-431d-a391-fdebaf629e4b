import re
import time
import os
import glob
import argparse
import subprocess
from collections import defaultdict
from pyspark.sql import SparkSession, functions as F

n_cores = os.cpu_count() or 192

def mk_spark(app_name):
    builder = (
        SparkSession.builder.appName(app_name)
        .remote("sc://localhost:15002")
        .config("spark.sql.session.timeZone", "UTC")
        .config("spark.sql.shuffle.partitions", f"{max(200, n_cores * 2)}")
        .config("spark.sql.adaptive.enabled", "true")
        .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
        .config("spark.sql.adaptive.localShuffleReader.enabled", "true")
        .config("spark.sql.adaptive.skewJoin.enabled", "true")
        .config("spark.sql.parquet.enableVectorizedReader", "true")
        .config("spark.sql.codegen.wholeStage", "true")
    )
    return builder.getOrCreate()


def to_abs(path):
    return os.path.abspath(path)


def ext_of(path: str) -> str:
    m = re.search(r"\.([A-Za-z0-9]+)$", path)
    return m.group(1).lower() if m else ""


def split_inputs(paths):
    json_like, parquet_like = [], []
    for p in paths:
        e = ext_of(p)
        if e in ("jsonl", "json"):
            json_like.append(to_abs(p))
        elif e == "parquet":
            parquet_like.append(to_abs(p))
    return json_like, parquet_like


def add_source_base(df):
    """
    用完整路径的哈希值作为 source_base，保证唯一性。
    例如：
      /data/a.jsonl   -> source_base = <sha1>
      /data/b/a.jsonl -> source_base = <sha1> (不同哈希，不会冲突)
    """
    return df.withColumn("source_base", F.sha1(F.input_file_name()))


def assign_group_and_write(df, gmin, gmax, out_root):
    """
    随机为每行分配 group_id，并按 group_id 分区写出。
    输出目录：{out_root}/group_id=.../part-*
    """
    if df is None:
        return
    span = gmax - gmin + 1
    df2 = df.select(
        "*", F.expr(f"CAST(floor(rand()*{span}) + {gmin} AS INT)").alias("group_id")
    )
    (
        df2.write.mode("overwrite")
        .format("json")
        .option("compression", "none")
        .partitionBy("group_id")
        .save(out_root)
    )

    # --- 写出后统一改名为 .jsonl ---
    for filepath in glob.glob(os.path.join(out_root, "group_id=*/*")):
        if filepath.endswith(".json") or ".json" in filepath:
            newpath = re.sub(r"\.json(\..*)?$", ".jsonl", filepath)  # 替换后缀
            os.rename(filepath, newpath)
    print(f"Done: wrote to {out_root}")


def count_lines(filename: str) -> int:
    """统计文件行数，调用系统 wc -l"""
    out = subprocess.check_output(["wc", "-l", filename])
    return int(out.strip().split()[0])

def list_generated_files(path):
    print("\nGenerated file summary:")
    # 统计 group 维度
    stats = {"groups": defaultdict(lambda: {"files": 0, "lines": 0}), "files": 0, "lines": 0}

    for root, _, files in os.walk(path):
        for f in files:
            if f.startswith(".") or f.startswith("_"):  # 跳过 _SUCCESS、crc 等
                continue
            filepath = os.path.join(root, f)
            parts = root.split(os.sep)
            group = next((p for p in parts if p.startswith("group_id=")), None)

            n_lines = count_lines(filepath)
            stats["files"] += 1
            stats["lines"] += n_lines
            if group:
                stats["groups"][group]["files"] += 1
                stats["groups"][group]["lines"] += n_lines

    # 打印总览
    print(f"{'#Groups':<10} {'#Files':<10} {'#Lines':<12}")
    print("-" * 40)
    print(f"{len(stats['groups']):<10} {stats['files']:<10} {stats['lines']:<12}")

    # 打印逐组明细
    if stats["groups"]:
        print("\nPer-group summary:")
        print(f"{'Group':<15} {'#Files':<10} {'#Lines':<12}")
        print("-" * 40)
        for g, info in sorted(stats["groups"].items(), key=lambda x: x[0]):
            print(f"{g:<15} {info['files']:<10} {info['lines']:<12}")


def main():
    ap = argparse.ArgumentParser("Random split (Spark Connect, jsonl/parquet)")
    ap.add_argument(
        "--inputs",
        nargs="+",
        required=True,
        help="Input paths (jsonl/parquet), support globs",
    )
    ap.add_argument("--output", type=str, required=True, help="Output base path")
    ap.add_argument(
        "--group-min", type=int, required=True, help="Min group id (inclusive)"
    )
    ap.add_argument(
        "--group-max", type=int, required=True, help="Max group id (inclusive)"
    )
    args = ap.parse_args()

    if args.group_min > args.group_max:
        raise ValueError("group-min must be <= group-max")

    out_abs = to_abs(args.output)
    # --- 执行前检查 ---
    if os.path.exists(out_abs) and any(os.scandir(out_abs)):
        raise RuntimeError(
            f"❌ Output directory {out_abs} is not empty. Please clean it before running."
        )

    start_time = time.time()
    print(
        f"Job started at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}"
    )
    spark = mk_spark("spark-data-split-random")

    json_like, parquet_like = split_inputs(args.inputs)
    print("Input jsonl/json files:", json_like)
    print("Input parquet files   :", parquet_like)
    if not json_like and not parquet_like:
        raise ValueError("No readable inputs (jsonl/json/parquet).")

    # 读取 jsonl
    json_df = None
    if json_like:
        json_df = spark.read.format("json").option("multiLine", "false").load(json_like)
        # json_df = add_source_base(json_df)

    # 读取 parquet
    parquet_df = None
    if parquet_like:
        parquet_df = spark.read.parquet(*parquet_like)
        # parquet_df = add_source_base(parquet_df)

    if json_df is not None and parquet_df is not None:
        union_df = json_df.unionByName(parquet_df, allowMissingColumns=True)
    elif json_df is not None:
        union_df = json_df
    else:
        union_df = parquet_df

    # 分配随机 group 并写出
    if union_df is not None:
        assign_group_and_write(union_df, args.group_min, args.group_max, out_abs)

    spark.stop()
    end_time = time.time()
    print(
        f"\nJob finished at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}"
    )
    print(f"Total elapsed: {end_time - start_time:.2f} seconds")
    # --- 执行后列出生成文件 ---
    list_generated_files(out_abs)
    print("All done.")


if __name__ == "__main__":
    main()
