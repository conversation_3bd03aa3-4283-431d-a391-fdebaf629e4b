import json
import os
import multiprocessing as mp
from pathlib import Path
from dataflow.operators.generate import CorpusTextSplitterBatch
from dataflow.utils.storage import FileStorage
import datetime


def split_jsonl_file(input_file, num_processes):
    """将JSONL文件按行数切分为多个子文件"""
    # 统计总行数
    with open(input_file, 'r', encoding='utf-8') as f:
        total_lines = sum(1 for _ in f)

    lines_per_process = total_lines // num_processes
    remaining_lines = total_lines % num_processes

    split_files = []
    input_dir = Path(input_file).parent
    input_stem = Path(input_file).stem

    with open(input_file, 'r', encoding='utf-8') as f:
        for i in range(num_processes):
            # 计算当前进程应该处理的行数
            current_lines = lines_per_process + (1 if i < remaining_lines else 0)

            # 创建子文件名
            split_file = input_dir / f"{input_stem}_part_{i}.jsonl"
            split_files.append(str(split_file))

            # 写入子文件
            with open(split_file, 'w', encoding='utf-8') as split_f:
                for _ in range(current_lines):
                    line = f.readline()
                    if line:
                        split_f.write(line)

    return split_files


def process_chunk_worker(args):
    """工作进程函数"""
    split_file, process_id, time_now, tokenizer_name = args

    try:
        # 为每个进程创建独立的存储配置
        storage = FileStorage(
            first_entry_file_name=split_file,
            cache_path=f"/home/<USER>/datasets/cache/chunked/longdata/{time_now}/process_{process_id}",
            file_name_prefix=f"langdata_step_p{process_id}",
            cache_type="jsonl",
        )

        knowledge_cleaning_step = CorpusTextSplitterBatch(
            split_method="sentence",
            chunk_size=16384,
            min_tokens_per_chunk=16384,
            tokenizer_name=tokenizer_name,
        )

        # 执行处理
        knowledge_cleaning_step.run(
            storage=storage.step(),
            input_key="raw_content"
        )

        print(f"进程 {process_id} 完成处理文件: {split_file}")
        return f"进程 {process_id} 成功"

    except Exception as e:
        print(f"进程 {process_id} 处理失败: {str(e)}")
        return f"进程 {process_id} 失败: {str(e)}"


def merge_results(time_now, num_processes):
    """合并所有进程的结果文件"""
    base_output_dir = Path(f"/home/<USER>/datasets/cache/chunked/longdata/{time_now}")
    merged_output_dir = base_output_dir / "merged"
    merged_output_dir.mkdir(parents=True, exist_ok=True)

    # 收集所有结果文件
    all_result_files = []
    for i in range(num_processes):
        process_dir = base_output_dir / f"process_{i}"
        if process_dir.exists():
            result_files = list(process_dir.glob("*.jsonl"))
            all_result_files.extend(result_files)

    # 按文件名排序并合并
    all_result_files.sort()
    merged_file = merged_output_dir / "merged_results.jsonl"

    with open(merged_file, 'w', encoding='utf-8') as merged_f:
        for result_file in all_result_files:
            with open(result_file, 'r', encoding='utf-8') as f:
                for line in f:
                    merged_f.write(line)

    print(f"结果已合并到: {merged_file}")
    return str(merged_file)


def cleanup_temp_files(split_files):
    """清理临时分割文件"""
    for split_file in split_files:
        try:
            os.remove(split_file)
        except:
            pass


class ParallelChunkPipeline:
    def __init__(self, num_processes=None, tokenizer_name="/home/<USER>/qwen/Qwen3-4B-Base"):
        """
        初始化并行处理管道

        Args:
            num_processes: 进程数，默认为CPU核心数
            tokenizer_name: 分词器路径
        """
        self.num_processes = num_processes or mp.cpu_count()
        self.tokenizer_name = tokenizer_name
        self.time_now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

        print(f"初始化并行处理管道，使用 {self.num_processes} 个进程")

    def forward(self, entry_file_name, cleanup_temp=True, merge_results_flag=True):
        """
        执行并行处理

        Args:
            entry_file_name: 输入文件路径
            cleanup_temp: 是否清理临时文件
            merge_results_flag: 是否合并结果
        """
        print(f"开始处理文件: {entry_file_name}")

        # 1. 分割输入文件
        print("正在分割输入文件...")
        split_files = split_jsonl_file(entry_file_name, self.num_processes)
        print(f"文件已分割为 {len(split_files)} 个部分")

        # 2. 准备进程参数
        process_args = [
            (split_files[i], i, self.time_now, self.tokenizer_name)
            for i in range(self.num_processes)
        ]

        # 3. 并行处理
        print("开始并行处理...")
        with mp.Pool(processes=self.num_processes) as pool:
            results = pool.map(process_chunk_worker, process_args)

        # 4. 输出处理结果
        print("\n处理结果:")
        for result in results:
            print(f"  - {result}")

        # 5. 合并结果（可选）
        if merge_results_flag:
            print("\n正在合并结果...")
            merged_file = merge_results(self.time_now, self.num_processes)
            print(f"合并完成: {merged_file}")

        # 6. 清理临时文件（可选）
        if cleanup_temp:
            print("\n清理临时文件...")
            cleanup_temp_files(split_files)
            print("临时文件清理完成")

        print(f"\n所有处理完成！输出目录: /home/<USER>/datasets/cache/chunked/longdata/{self.time_now}")


# 使用示例
if __name__ == "__main__":
    # 配置参数
    entry_file_name = "/home/<USER>/datasets/LongData-Corpus/85000_md.jsonl"

    # 创建并行处理管道
    # num_processes=4 表示使用4个进程，可以根据需要调整
    pipeline = ParallelChunkPipeline(
        num_processes=4,  # 可以根据CPU核心数调整
        tokenizer_name="/home/<USER>/qwen/Qwen3-4B-Base"
    )

    # 执行处理
    pipeline.forward(
        entry_file_name=entry_file_name,
        cleanup_temp=True,  # 是否清理临时分割文件
        merge_results_flag=True  # 是否合并最终结果
    )