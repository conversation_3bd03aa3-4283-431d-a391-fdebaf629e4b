#!/bin/bash

# JSONL转Markdown脚本
# 抽取 jsonl 中 text 字段到 markdown 文件，用于小说文本处理
# 用法: ./jsonl_to_md.sh [输入目录] [输出目录]

# 默认参数
INPUT_DIR="${1:-.}"          # 输入目录，默认当前目录
OUTPUT_DIR="${2:-./output}"  # 输出目录，默认./output

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    local missing_deps=()

    # 检查jq
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi

    # 检查python3（备用方案）
    if ! command -v python3 &> /dev/null; then
        log_warning "python3 未安装，将只使用jq处理"
    fi

    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        log_error "请安装: sudo apt install ${missing_deps[*]}"
        exit 1
    fi
}

# 创建输出目录
create_output_dir() {
    if [ ! -d "$OUTPUT_DIR" ]; then
        mkdir -p "$OUTPUT_DIR"
        log_info "创建输出目录: $OUTPUT_DIR"
    fi
}

# 清理文件名（移除特殊字符）
sanitize_filename() {
    local filename="$1"
    # 移除或替换特殊字符
    echo "$filename" | sed 's/[<>:"/\\|?*]/_/g' | sed 's/[[:space:]]/_/g' | cut -c1-100
}

# 使用jq提取text字段
extract_with_jq() {
    local jsonl_file="$1"
    local base_name="$2"
    local count=0
    local success_count=0

    log_info "使用jq处理: $jsonl_file"

    while IFS= read -r line; do
        if [ -n "$line" ]; then
            count=$((count + 1))

            # 提取text字段
            text_content=$(echo "$line" | jq -r '.text // empty' 2>/dev/null)

            if [ -n "$text_content" ] && [ "$text_content" != "null" ]; then
                # 生成文件名
                local md_filename="${base_name}_${count}.md"
                local output_file="$OUTPUT_DIR/$md_filename"

                # 写入markdown文件
                 echo "$text_content" > "$output_file"

                success_count=$((success_count + 1))

                # 每100个文件显示一次进度
                if [ $((count % 100)) -eq 0 ]; then
                    log_info "已处理 $count 行，成功提取 $success_count 个text字段"
                fi
            else
                log_warning "第 $count 行没有找到有效的text字段"
            fi
        fi
    done < "$jsonl_file"

    log_success "文件 $jsonl_file 处理完成: $success_count/$count 个文档成功提取"
    return $success_count
}

# 使用Python提取text字段（备用方案）
extract_with_python() {
    local jsonl_file="$1"
    local base_name="$2"

    log_info "使用Python处理: $jsonl_file"

    python3 << EOF
import json
import os
import re
from datetime import datetime

def sanitize_filename(filename):
    # 移除特殊字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    filename = re.sub(r'\s+', '_', filename)
    return filename[:100]  # 限制长度

count = 0
success_count = 0
base_name = "$base_name"
output_dir = "$OUTPUT_DIR"

with open("$jsonl_file", 'r', encoding='utf-8') as f:
    for line_num, line in enumerate(f, 1):
        line = line.strip()
        if not line:
            continue

        try:
            data = json.loads(line)
            text_content = data.get('text', '')

            if text_content:
                md_filename = f"{base_name}_{line_num}.md"
                output_file = os.path.join(output_dir, md_filename)

                with open(output_file, 'w', encoding='utf-8') as md_file:
                    md_file.write(f"# Document {line_num} from {base_name}.jsonl\n\n")
                    md_file.write(f"**Source:** \`$jsonl_file\`\n")
                    md_file.write(f"**Line:** {line_num}\n")
                    md_file.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    md_file.write("---\n\n")
                    md_file.write(text_content)

                success_count += 1

                if line_num % 100 == 0:
                    print(f"[INFO] 已处理 {line_num} 行，成功提取 {success_count} 个text字段")
            else:
                print(f"[WARNING] 第 {line_num} 行没有找到有效的text字段")

            count = line_num

        except json.JSONDecodeError as e:
            print(f"[ERROR] 第 {line_num} 行JSON解析错误: {e}")
        except Exception as e:
            print(f"[ERROR] 第 {line_num} 行处理错误: {e}")

print(f"[SUCCESS] 文件 $jsonl_file 处理完成: {success_count}/{count} 个文档成功提取")
EOF
}

# 处理单个JSONL文件
process_jsonl_file() {
    local jsonl_file="$1"
    local base_name=$(basename "$jsonl_file" .jsonl)

    log_info "开始处理文件: $jsonl_file"

    # 检查文件是否存在
    if [ ! -f "$jsonl_file" ]; then
        log_error "文件不存在: $jsonl_file"
        return 1
    fi

    # 检查文件是否为空
    if [ ! -s "$jsonl_file" ]; then
        log_warning "文件为空: $jsonl_file"
        return 1
    fi

    # 优先使用jq，如果失败则使用python
    if command -v jq &> /dev/null; then
        extract_with_jq "$jsonl_file" "$base_name"
    elif command -v python3 &> /dev/null; then
        extract_with_python "$jsonl_file" "$base_name"
    else
        log_error "没有可用的JSON处理工具"
        return 1
    fi
}

# 显示使用说明
show_usage() {
    echo "JSONL转Markdown脚本"
    echo ""
    echo "用法:"
    echo "  $0 [输入目录] [输出目录]"
    echo ""
    echo "参数:"
    echo "  输入目录    包含JSONL文件的目录 (默认: 当前目录)"
    echo "  输出目录    生成的Markdown文件保存目录 (默认: ./output)"
    echo ""
    echo "示例:"
    echo "  $0                          # 处理当前目录的所有JSONL文件"
    echo "  $0 ./data ./markdown        # 处理./data目录，输出到./markdown"
    echo "  $0 file.jsonl ./output      # 处理单个文件"
    echo ""
    echo "依赖:"
    echo "  - jq (推荐) 或 python3"
    echo "  - 安装: sudo apt install jq"
}

# 主函数
main() {
    # 显示欢迎信息
    echo "================================================"
    echo "           JSONL 转 Markdown 脚本"
    echo "================================================"
    echo ""

    # 检查帮助参数
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_usage
        exit 0
    fi

    # 检查依赖
    check_dependencies

    # 创建输出目录
    create_output_dir

    log_info "输入路径: $INPUT_DIR"
    log_info "输出目录: $OUTPUT_DIR"
    echo ""

    local total_files=0
    local processed_files=0

    # 判断输入是文件还是目录
    if [ -f "$INPUT_DIR" ]; then
        # 单个文件
        if [[ "$INPUT_DIR" == *.jsonl ]]; then
            total_files=1
            log_info "处理单个文件: $INPUT_DIR"
            if process_jsonl_file "$INPUT_DIR"; then
                processed_files=1
            fi
        else
            log_error "输入文件不是JSONL格式: $INPUT_DIR"
            exit 1
        fi
    elif [ -d "$INPUT_DIR" ]; then
        # 目录
        log_info "搜索JSONL文件..."

        # 查找所有JSONL文件
        while IFS= read -r -d '' jsonl_file; do
            total_files=$((total_files + 1))
            if process_jsonl_file "$jsonl_file"; then
                processed_files=$((processed_files + 1))
            fi
            echo ""
        done < <(find "$INPUT_DIR" -name "*.jsonl" -type f -print0)

        if [ $total_files -eq 0 ]; then
            log_warning "在目录 $INPUT_DIR 中没有找到JSONL文件"
            exit 1
        fi
    else
        log_error "输入路径不存在: $INPUT_DIR"
        exit 1
    fi

    echo ""
    echo "================================================"
    log_success "处理完成!"
    log_success "总文件数: $total_files"
    log_success "成功处理: $processed_files"
    log_success "输出目录: $OUTPUT_DIR"
    echo "================================================"
}

# 执行主函数
main "$@"