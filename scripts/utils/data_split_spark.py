# file: split_random_connect.py
import re
import os
import argparse
from pyspark.sql import SparkSession, functions as F

def mk_spark(app_name):
    builder = (
        SparkSession.builder
            .appName(app_name)
            .remote("sc://localhost:15002")
            .config("spark.sql.session.timeZone", "UTC")
            .config("spark.sql.shuffle.partitions", "200")
            .config("spark.sql.adaptive.enabled", "true")
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
            .config("spark.sql.adaptive.localShuffleReader.enabled", "true")
            .config("spark.sql.adaptive.skewJoin.enabled", "true")
            .config("spark.sql.parquet.enableVectorizedReader", "true")
            .config("spark.sql.codegen.wholeStage", "true")
    )
    return builder.getOrCreate()

def to_abs(path):
    return os.path.abspath(path)

def ext_of(path: str) -> str:
    m = re.search(r'\.([A-Za-z0-9]+)$', path)
    return (m.group(1).lower() if m else "")

def split_inputs(paths):
    json_like, parquet_like = [], []
    for p in paths:
        e = ext_of(p)
        if e in ("jsonl", "json"):
            json_like.append(to_abs(p))
        elif e == "parquet":
            parquet_like.append(to_abs(p))
    return json_like, parquet_like

def add_source_base(df):
    """
    用完整路径的哈希值作为 source_base，保证唯一性。
    例如：
      /data/a.jsonl   -> source_base = <sha1>
      /data/b/a.jsonl -> source_base = <sha1> (不同哈希，不会冲突)
    """
    return df.withColumn("source_base", F.sha1(F.input_file_name()))

def assign_group_and_write(df, gmin, gmax, out_root, out_fmt, label):
    """
    随机为每行分配 group_id，并按 (source_base, group_id) 分区写出。
    输出目录：{out_root}/format={label}/source_base=.../group_id=.../part-*
    """
    if df is None:
        return
    span = gmax - gmin + 1
    # 纯随机：floor(rand()*span)+gmin
    df2 = df.select(
        "*",
        F.expr(f"CAST(floor(rand()*{span}) + {gmin} AS INT)").alias("group_id")
    )
    base_out = os.path.join(os.path.abspath(out_root), f"format={label}")
    (df2.repartition("source_base", "group_id")
        .write
        .mode("overwrite")
        .format(out_fmt)
        .option("compression", "none" if out_fmt=="json" else "snappy")
        .partitionBy("source_base", "group_id")
        .save(base_out))
    print(f"Done: {label} -> {base_out}")

def main():
    ap = argparse.ArgumentParser("Random split (Spark Connect, jsonl/parquet)")
    ap.add_argument("--inputs", nargs="+", required=True, help="Input paths (jsonl/parquet), support globs")
    ap.add_argument("--output", type=str, required=True, help="Output base path")
    ap.add_argument("--group-min", type=int, required=True, help="Min group id (inclusive)")
    ap.add_argument("--group-max", type=int, required=True, help="Max group id (inclusive)")
    args = ap.parse_args()

    if args.group_min > args.group_max:
        raise ValueError("group-min must be <= group-max")

    spark = mk_spark("spark-data-split-random")

    json_like, parquet_like = split_inputs(args.inputs)
    print("Input jsonl/json files:", json_like)
    print("Input parquet files   :", parquet_like)
    if not json_like and not parquet_like:
        raise ValueError("No readable inputs (jsonl/json/parquet).")

    # 读取 jsonl
    json_df = None
    if json_like:
        json_df = spark.read.format("json").option("multiLine", "false").load(json_like)
        json_df = add_source_base(json_df)

    # 读取 parquet
    parquet_df = None
    if parquet_like:
        parquet_df = spark.read.parquet(*parquet_like)
        parquet_df = add_source_base(parquet_df)
    
    # 分配随机 group 并写出
    if json_df is not None:
        assign_group_and_write(
            json_df, args.group_min, args.group_max, args.output, out_fmt="json", label="jsonl"
        )
    if parquet_df is not None:
        assign_group_and_write(
            parquet_df, args.group_min, args.group_max, args.output, out_fmt="parquet", label="parquet"
        )

    spark.stop()
    print("All done.")

if __name__ == "__main__":
    main()