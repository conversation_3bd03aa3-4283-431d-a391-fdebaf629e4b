#!/usr/bin/env python3
"""
JSONL文件Source分布统计工具
用于统计分割后的JSONL文件中各个source的数量分布
"""

import orjson
import os
import argparse
from collections import defaultdict, Counter
from pathlib import Path
import sys


def analyze_single_file(file_path):
    """分析单个JSONL文件的source分布"""
    source_counts = Counter()
    total_lines = 0
    invalid_lines = 0

    try:
        with open(file_path, 'r', encoding='utf-8', buffering=8192 * 16) as f:
            for line_no, line in enumerate(f, 1):
                total_lines += 1
                try:
                    data = orjson.loads(line.strip())
                    source = data.get('source', 'unknown')
                    source_counts[source] += 1
                except orjson.JSONDecodeError:
                    invalid_lines += 1
                    continue
                except Exception:
                    invalid_lines += 1
                    continue

    except Exception as e:
        print(f"❌ 读取文件失败 {file_path}: {e}")
        return None, 0, 0

    return source_counts, total_lines, invalid_lines


def analyze_directory(directory_path, pattern="*.jsonl"):
    """分析目录中所有JSONL文件的source分布"""
    directory = Path(directory_path)

    if not directory.exists():
        print(f"❌ 目录不存在: {directory_path}")
        return False

    jsonl_files = sorted(directory.glob(pattern))

    if not jsonl_files:
        print(f"❌ 在目录 {directory_path} 中未找到匹配 {pattern} 的文件")
        return False

    print(f"📁 分析目录: {directory_path}")
    print(f"📄 发现 {len(jsonl_files)} 个文件")
    print("=" * 80)

    # 存储所有文件的统计结果
    all_file_stats = {}
    grand_total_sources = Counter()
    grand_total_records = 0
    grand_invalid_lines = 0

    # 分析每个文件
    for file_path in jsonl_files:
        print(f"\n📊 分析文件: {file_path.name}")

        source_counts, total_lines, invalid_lines = analyze_single_file(file_path)

        if source_counts is None:
            continue

        valid_records = total_lines - invalid_lines
        all_file_stats[file_path.name] = {
            'source_counts': source_counts,
            'total_lines': total_lines,
            'valid_records': valid_records,
            'invalid_lines': invalid_lines
        }

        # 累加到总计
        grand_total_sources.update(source_counts)
        grand_total_records += valid_records
        grand_invalid_lines += invalid_lines

        # 显示当前文件统计
        print(f"  📈 总行数: {total_lines:,}")
        print(f"  ✅ 有效记录: {valid_records:,}")
        if invalid_lines > 0:
            print(f"  ⚠️  无效行数: {invalid_lines:,}")

        print(f"  🏷️  Source分布:")
        for source, count in source_counts.most_common():
            percentage = (count / valid_records * 100) if valid_records > 0 else 0
            print(f"     {source:20s}: {count:8,} ({percentage:5.1f}%)")

    # 显示汇总统计
    print("\n" + "=" * 80)
    print("📋 汇总统计")
    print("=" * 80)

    print(f"📁 总文件数: {len(all_file_stats)}")
    print(f"📊 总有效记录: {grand_total_records:,}")
    if grand_invalid_lines > 0:
        print(f"⚠️  总无效行数: {grand_invalid_lines:,}")

    print(f"\n🏷️  总体Source分布:")
    for source, count in grand_total_sources.most_common():
        percentage = (count / grand_total_records * 100) if grand_total_records > 0 else 0
        print(f"   {source:25s}: {count:10,} ({percentage:6.2f}%)")

    # 显示各文件间的分布对比
    print(f"\n📊 各文件Source分布对比:")
    print(f"{'File':20s}", end="")

    # 获取所有source名称，按总数排序
    all_sources = [source for source, _ in grand_total_sources.most_common()]

    # 打印表头
    for source in all_sources:
        print(f"{source:>12s}", end="")
    print(f"{'Total':>12s}")

    print("-" * (20 + 12 * (len(all_sources) + 1)))

    # 打印每个文件的数据
    for file_name in sorted(all_file_stats.keys()):
        stats = all_file_stats[file_name]
        print(f"{file_name:20s}", end="")

        for source in all_sources:
            count = stats['source_counts'].get(source, 0)
            print(f"{count:12,}", end="")

        print(f"{stats['valid_records']:12,}")

    # 打印总计行
    print("-" * (20 + 12 * (len(all_sources) + 1)))
    print(f"{'TOTAL':20s}", end="")
    for source in all_sources:
        count = grand_total_sources[source]
        print(f"{count:12,}", end="")
    print(f"{grand_total_records:12,}")

    return True


def analyze_single_file_command(file_path):
    """分析单个文件的命令"""
    print(f"📄 分析单个文件: {file_path}")
    print("=" * 60)

    source_counts, total_lines, invalid_lines = analyze_single_file(file_path)

    if source_counts is None:
        return False

    valid_records = total_lines - invalid_lines

    print(f"📈 总行数: {total_lines:,}")
    print(f"✅ 有效记录: {valid_records:,}")
    if invalid_lines > 0:
        print(f"⚠️  无效行数: {invalid_lines:,}")

    print(f"\n🏷️  Source分布:")
    for source, count in source_counts.most_common():
        percentage = (count / valid_records * 100) if valid_records > 0 else 0
        print(f"   {source:25s}: {count:8,} ({percentage:6.2f}%)")

    return True


def main():
    parser = argparse.ArgumentParser(
        description='JSONL文件Source分布统计工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 分析单个文件
  python source_stats.py file.jsonl

  # 分析目录中的所有jsonl文件
  python source_stats.py /path/to/splits/

  # 分析特定模式的文件
  python source_stats.py /path/to/splits/ --pattern "split_*.jsonl"

  # 输出到文件
  python source_stats.py /path/to/splits/ > stats_report.txt
        """
    )

    parser.add_argument('path', help='要分析的文件或目录路径')
    parser.add_argument('--pattern', default='*.jsonl',
                        help='文件匹配模式 (默认: *.jsonl)')

    args = parser.parse_args()

    path = Path(args.path)

    if not path.exists():
        print(f"❌ 路径不存在: {args.path}")
        sys.exit(1)

    print("🔍 JSONL Source分布统计工具")
    print("=" * 80)

    if path.is_file():
        # 分析单个文件
        success = analyze_single_file_command(args.path)
    elif path.is_dir():
        # 分析目录
        success = analyze_directory(args.path, args.pattern)
    else:
        print(f"❌ 无效的路径类型: {args.path}")
        sys.exit(1)

    if success:
        print(f"\n✅ 分析完成!")
    else:
        print(f"\n❌ 分析失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()