import orjson
import os
import random
import hashlib
from collections import defaultdict, Counter
from pathlib import Path
import argparse
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import pickle
import tempfile
import shutil


class LargeJSONLSampler:
    def __init__(self, input_dir, output_dir, num_splits=10, sample_ratio=1.0, max_workers=None):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.num_splits = num_splits
        self.sample_ratio = sample_ratio
        self.max_workers = max_workers or min(mp.cpu_count(), 8)
        self.output_dir.mkdir(exist_ok=True)

    def step1_analyze_sources(self):
        """第一步：多进程扫描所有文件，统计每个source的数量分布"""
        print("Step 1: 分析数据源分布...")

        jsonl_files = list(self.input_dir.glob("*.jsonl"))
        print(f"发现 {len(jsonl_files)} 个JSONL文件，使用 {self.max_workers} 个进程并行处理")

        source_counts = Counter()
        file_source_mapping = defaultdict(set)

        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {
                executor.submit(analyze_single_file, str(file)): file
                for file in jsonl_files
            }

            for future in as_completed(future_to_file):
                file = future_to_file[future]
                try:
                    file_source_counts, file_sources = future.result()
                    print(f"✓ 完成分析: {file.name} ({len(file_sources)} 个源)")

                    source_counts.update(file_source_counts)
                    file_source_mapping[str(file)] = file_sources

                except Exception as exc:
                    print(f"✗ 分析文件失败 {file}: {exc}")

        print(f"发现 {len(source_counts)} 个不同的数据源")
        print("数据源分布:")
        for source, count in source_counts.most_common():
            print(f"  {source}: {count:,} 条记录")

        return source_counts, file_source_mapping

    def step2_calculate_sampling_strategy(self, source_counts):
        """第二步：计算分层抽样策略"""
        print("\nStep 2: 计算分层抽样策略...")

        total_records = sum(source_counts.values())
        target_total = int(total_records * self.sample_ratio)
        target_per_split = target_total // self.num_splits

        print(f"总记录数: {total_records:,}")
        print(f"目标抽样总数: {target_total:,}")
        print(f"每个分割目标数: {target_per_split:,}")

        source_per_split = {}
        source_sampling_rates = {}

        for source, count in source_counts.items():
            source_ratio = count / total_records
            source_target_total = int(target_total * source_ratio)
            source_target_per_split = source_target_total // self.num_splits

            source_per_split[source] = source_target_per_split
            source_sampling_rates[source] = source_target_total / count if count > 0 else 0

            print(f"  {source}: {count:,} -> {source_target_total:,} "
                  f"(抽样率: {source_sampling_rates[source]:.3f}, 每分割: {source_target_per_split:,})")

        return source_per_split, source_sampling_rates

    def step3_direct_hash_sampling(self, file_source_mapping, source_per_split, source_sampling_rates):
        """第三步：基于哈希的直接分割抽样（避免全局样本池）"""
        print("\nStep 3: 执行基于哈希的分割抽样...")

        # 准备临时文件
        temp_dir = tempfile.mkdtemp(prefix="jsonl_sampling_")
        print(f"使用临时目录: {temp_dir}")

        try:
            # 为每个分割准备文件处理任务
            file_tasks = []
            for file_path in file_source_mapping.keys():
                file_tasks.append((
                    file_path,
                    file_source_mapping[file_path],
                    source_per_split,
                    source_sampling_rates,
                    temp_dir,
                    self.num_splits
                ))

            # 多进程处理文件
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [
                    executor.submit(process_file_with_hash_split, *task)
                    for task in file_tasks
                ]

                # 等待所有文件处理完成
                for future in as_completed(futures):
                    try:
                        file_path, file_counts = future.result()
                        print(f"✓ 完成处理: {Path(file_path).name}")
                    except Exception as exc:
                        print(f"✗ 处理文件失败: {exc}")

            # 合并临时文件到最终输出
            self._merge_temp_files(temp_dir)

        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)

        print(f"抽样完成！结果保存在: {self.output_dir}")

    def _merge_temp_files(self, temp_dir):
        """合并临时文件到最终输出"""
        print("合并临时文件...")

        split_counts = [defaultdict(int) for _ in range(self.num_splits)]

        # 为每个分割创建最终输出文件
        output_files = []
        for i in range(self.num_splits):
            output_file = self.output_dir / f"split_{i:02d}.jsonl"
            output_files.append(open(output_file, 'w', encoding='utf-8'))

        try:
            # 合并每个分割的临时文件
            for split_idx in range(self.num_splits):
                temp_pattern = f"split_{split_idx}_*.jsonl"
                temp_files = list(Path(temp_dir).glob(temp_pattern))

                for temp_file in temp_files:
                    with open(temp_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                # 统计source分布
                                data = orjson.loads(line.strip())
                                source = data.get('source', 'unknown')
                                split_counts[split_idx][source] += 1

                                # 写入最终文件
                                output_files[split_idx].write(line)
                            except:
                                continue

            # 显示分配结果
            for split_idx in range(self.num_splits):
                total_in_split = sum(split_counts[split_idx].values())
                print(f"分割 {split_idx + 1}: {total_in_split:,} 条记录")
                for source, count in split_counts[split_idx].items():
                    if count > 0:
                        print(f"    {source}: {count:,}")

        finally:
            for f in output_files:
                f.close()

    def run(self):
        """运行完整的抽样流程"""
        # Step 1: 分析数据源
        # source_counts: 每个source的总数量
        # file_source_mapping: 每个文件包含的source集合
        source_counts, file_source_mapping = self.step1_analyze_sources()

        # Step 2: 计算抽样策略
        # 每分割的目标数量，以及每个source的抽样率
        source_per_split, source_sampling_rates = self.step2_calculate_sampling_strategy(source_counts)

        # Step 3: 直接哈希分割抽样
        # 根据哈希值直接将数据写入对应的分割文件，避免了全局样本池的内存开销
        self.step3_direct_hash_sampling(file_source_mapping, source_per_split, source_sampling_rates)

        return True


# 多进程辅助函数
def analyze_single_file(file_path):
    """分析单个文件的数据源分布"""
    source_counts = Counter()
    file_sources = set()

    try:
        with open(file_path, 'r', encoding='utf-8', buffering=8192 * 16) as f:
            for line_no, line in enumerate(f):
                try:
                    data = orjson.loads(line.strip())
                    source = data.get('source', 'unknown')
                    source_counts[source] += 1
                    file_sources.add(source)
                except orjson.JSONDecodeError:
                    continue
                except Exception:
                    continue

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return Counter(), set()

    return source_counts, file_sources


def process_file_with_hash_split(file_path, file_sources, source_per_split, source_sampling_rates, temp_dir,
                                 num_splits):
    """使用哈希函数处理单个文件的分割抽样"""

    # 为每个分割创建临时文件
    temp_files = {}
    file_handles = {}
    split_counts = [defaultdict(int) for _ in range(num_splits)]
    source_reservoirs = [defaultdict(list) for _ in range(num_splits)]
    source_seen_counts = [defaultdict(int) for _ in range(num_splits)]

    try:
        # 打开临时文件
        for split_idx in range(num_splits):
            temp_file = Path(temp_dir) / f"split_{split_idx}_{Path(file_path).stem}.jsonl"
            file_handles[split_idx] = open(temp_file, 'w', encoding='utf-8')

        # 处理文件中的每一行
        with open(file_path, 'r', encoding='utf-8', buffering=8192 * 16) as f:
            for line_no, line in enumerate(f):
                try:
                    data = orjson.loads(line.strip())
                    source = data.get('source', 'unknown')

                    if source not in file_sources:
                        continue

                    # 使用行内容的哈希值决定分割
                    line_hash = hashlib.md5(line.strip().encode('utf-8')).hexdigest()
                    # 使用前8个字符的16进制值作为哈希值，然后对num_splits取模
                    split_idx = int(line_hash[:8], 16) % num_splits

                    # 获取source的目标数量和抽样率
                    target_count = source_per_split.get(source, 0)
                    sampling_rate = source_sampling_rates.get(source, 0)

                    # 如果目标数量和抽样率都大于0，则进行抽样
                    if target_count > 0 and sampling_rate > 0:
                        # 记录该source在该分割中出现的次数
                        source_seen_counts[split_idx][source] += 1

                        # 使用reservoir sampling进行抽样
                        reservoir = source_reservoirs[split_idx][source]

                        # 先决定是否要这个样本（基于抽样率）
                        if random.random() < sampling_rate:
                            if len(reservoir) < target_count:
                                reservoir.append(line.strip())
                            else:
                                # 随机替换
                                j = random.randint(0, len(reservoir) - 1)
                                reservoir[j] = line.strip()

                except orjson.JSONDecodeError:
                    continue
                except Exception:
                    continue

        # 将reservoir中的样本写入临时文件
        for split_idx in range(num_splits):
            for source, samples in source_reservoirs[split_idx].items():
                for sample in samples:
                    file_handles[split_idx].write(sample + '\n')
                    split_counts[split_idx][source] += 1

        # 统计结果
        total_splits_count = {}
        for split_idx in range(num_splits):
            total_splits_count[split_idx] = dict(split_counts[split_idx])

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return file_path, {}

    finally:
        # 关闭所有文件
        for f in file_handles.values():
            f.close()

    return file_path, total_splits_count


def main():
    parser = argparse.ArgumentParser(description='大规模JSONL文件分层抽样工具（快速哈希分割版）')
    parser.add_argument('input_dir', help='输入JSONL文件目录')
    parser.add_argument('output_dir', help='输出目录')
    parser.add_argument('--splits', type=int, default=10, help='分割数量 (默认: 10)')
    parser.add_argument('--ratio', type=float, default=1.0, help='抽样比例 (默认: 1.0, 即全量)')
    parser.add_argument('--seed', type=int, default=42, help='随机种子 (默认: 42)')
    parser.add_argument('--workers', type=int, default=None, help='并行进程数 (默认: min(CPU核心数, 8))')

    args = parser.parse_args()

    # 设置随机种子确保可重复性
    random.seed(args.seed)

    print(f"开始处理（快速哈希分割版）...")
    print(f"输入目录: {args.input_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"分割数量: {args.splits}")
    print(f"抽样比例: {args.ratio}")
    print(f"随机种子: {args.seed}")
    print(f"并行进程数: {args.workers or 'auto'}")
    print("-" * 50)

    sampler = LargeJSONLSampler(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        num_splits=args.splits,
        sample_ratio=args.ratio,
        max_workers=args.workers
    )

    success = sampler.run()

    if success:
        print("\n🎉 处理完成！数据无重复且速度更快！")

        # 验证无重复（可选）
        print("\n验证结果...")
        verify_no_duplicates(args.output_dir, args.splits)
    else:
        print("\n❌ 处理失败！")


def verify_no_duplicates(output_dir, num_splits):
    """验证分割间无重复数据"""
    print("验证数据一致性...")

    # 统计每个分割的记录数和source分布
    total_records = 0
    source_totals = defaultdict(int)

    for i in range(num_splits):
        split_file = Path(output_dir) / f"split_{i:02d}.jsonl"
        if split_file.exists():
            split_count = 0
            split_sources = defaultdict(int)

            with open(split_file, 'r') as f:
                for line in f:
                    try:
                        data = orjson.loads(line.strip())
                        source = data.get('source', 'unknown')
                        split_count += 1
                        split_sources[source] += 1
                        source_totals[source] += 1
                    except:
                        continue

            print(f"  Split {i + 1:2d}: {split_count:,} 条记录")
            total_records += split_count

    print(f"\n总计: {total_records:,} 条记录")
    print("各source分布:")
    for source, count in source_totals.items():
        print(f"  {source}: {count:,}")


if __name__ == "__main__":
    main()