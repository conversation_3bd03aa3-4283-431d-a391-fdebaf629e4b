import orjson
import os
import glob
import random
import math
import time
from collections import defaultdict
from typing import Dict, List, Tuple, Set, Optional, Iterator
import argparse
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from functools import partial


class ProgressTracker:
    """进度跟踪器，支持多线程安全的进度显示"""

    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = time.time()
        self.lock = threading.Lock()
        self.last_update = 0

    def update(self, amount: int = 1):
        with self.lock:
            self.current += amount
            current_time = time.time()
            # 限制更新频率，避免过于频繁的输出影响性能
            if current_time - self.last_update > 0.1 or self.current == self.total:
                self._display_progress()
                self.last_update = current_time

    def _display_progress(self):
        elapsed = time.time() - self.start_time
        if self.current > 0 and elapsed > 0:
            rate = self.current / elapsed
            eta = (self.total - self.current) / rate if rate > 0 else 0

            percentage = (self.current / self.total) * 100
            bar_length = 30
            filled_length = int(bar_length * self.current / self.total)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)

            print(f'\r{self.description}: {bar} {percentage:.1f}% '
                  f'({self.current}/{self.total}) '
                  f'Speed: {rate:.1f}/s ETA: {eta:.1f}s', end='', flush=True)

            if self.current == self.total:
                print(f'\n✓ {self.description} completed in {elapsed:.1f}s')


def read_single_file(file_path: str, progress_tracker: ProgressTracker) -> Tuple[List[dict], Dict[str, List[int]], int]:
    """
    读取单个JSONL文件

    Returns:
        tuple: (数据列表, source索引字典, 起始索引偏移量)
    """
    file_data = []
    file_source_indices = defaultdict(list)
    error_count = 0

    try:
        # 先获取文件大小用于预估进度
        file_size = os.path.getsize(file_path)

        with open(file_path, 'r', encoding='utf-8', buffering=8192 * 16) as f:  # 增大缓冲区
            for line_num, line in enumerate(f):
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                try:
                    data = orjson.loads(line)
                    if 'source' in data:
                        current_index = len(file_data)
                        file_data.append(data)
                        file_source_indices[data['source']].append(current_index)
                    else:
                        error_count += 1
                        if error_count <= 10:  # 只显示前10个错误
                            print(f"\n警告: {file_path} 第 {line_num + 1} 行缺少 'source' 字段")
                        elif error_count == 11:
                            print(f"\n警告: {file_path} 更多错误将不再显示...")

                except orjson.JSONDecodeError as e:
                    error_count += 1
                    if error_count <= 10:
                        print(f"\n警告: {file_path} 第 {line_num + 1} 行JSON解析错误: {e}")
                    elif error_count == 11:
                        print(f"\n警告: {file_path} 更多错误将不再显示...")

                # 每1000行更新一次进度（减少锁竞争）
                if (line_num + 1) % 1000 == 0:
                    progress_tracker.update(1000)

        # 更新剩余进度
        remainder = (line_num + 1) % 1000
        if remainder > 0:
            progress_tracker.update(remainder)

    except Exception as e:
        print(f"\n错误: 无法读取文件 {file_path}: {e}")
        return [], {}, 0

    return file_data, file_source_indices, error_count


def estimate_total_lines(files: List[str], sample_files: int = 3) -> int:
    """
    估算总行数，通过采样几个文件来预估
    """
    if not files:
        return 0

    sample_files = min(sample_files, len(files))
    sample_file_list = random.sample(files, sample_files) if len(files) > sample_files else files

    total_lines = 0
    total_size = 0

    for file_path in sample_file_list:
        try:
            file_size = os.path.getsize(file_path)
            line_count = 0

            with open(file_path, 'r', encoding='utf-8', buffering=8192 * 16) as f:
                for line in f:
                    line_count += 1

            total_lines += line_count
            total_size += file_size

        except Exception:
            continue

    if total_size > 0:
        # 计算所有文件的总大小
        all_files_size = sum(os.path.getsize(f) for f in files if os.path.exists(f))
        # 基于大小比例估算总行数
        estimated_lines = int((total_lines / total_size) * all_files_size)
        return estimated_lines

    return len(files) * 10000  # 默认估算


def read_jsonl_files_parallel(file_pattern: str, max_workers: int = 4) -> Tuple[List[dict], Dict[str, List[int]]]:
    """
    并行读取所有匹配的JSONL文件

    Args:
        file_pattern: 文件匹配模式
        max_workers: 最大并行工作线程数

    Returns:
        tuple: (所有数据列表, 按source分组的索引字典)
    """
    # 获取所有匹配的文件
    files = glob.glob(file_pattern)
    if not files:
        raise ValueError(f"没有找到匹配模式 '{file_pattern}' 的文件")

    # 按文件大小排序，大文件优先处理（更好的负载均衡）
    files.sort(key=lambda f: os.path.getsize(f) if os.path.exists(f) else 0, reverse=True)

    print(f"找到 {len(files)} 个JSONL文件")

    # 估算总行数用于进度显示
    print("正在估算总数据量...")
    estimated_lines = estimate_total_lines(files)
    print(f"预估总行数: {estimated_lines:,}")

    # 创建进度跟踪器
    progress = ProgressTracker(estimated_lines, "读取文件")

    all_data = []
    source_indices = defaultdict(list)
    total_errors = 0

    # 确定合适的并行度
    optimal_workers = min(max_workers, len(files), os.cpu_count() or 1)
    print(f"使用 {optimal_workers} 个线程并行读取")

    # 并行读取文件
    with ThreadPoolExecutor(max_workers=optimal_workers) as executor:
        # 提交所有任务
        future_to_file = {
            executor.submit(read_single_file, file_path, progress): file_path
            for file_path in files
        }

        # 收集结果
        for future in as_completed(future_to_file):
            file_path = future_to_file[future]
            try:
                file_data, file_source_indices, error_count = future.result()

                if file_data:
                    # 调整索引偏移量
                    base_index = len(all_data)
                    for source, indices in file_source_indices.items():
                        adjusted_indices = [idx + base_index for idx in indices]
                        source_indices[source].extend(adjusted_indices)

                    all_data.extend(file_data)
                    total_errors += error_count

            except Exception as e:
                print(f"\n错误: 处理文件 {file_path} 时发生异常: {e}")

    print(f"\n总共读取 {len(all_data):,} 条有效数据")
    if total_errors > 0:
        print(f"总计 {total_errors} 条错误记录")

    print(f"发现 {len(source_indices)} 个不同的source:")
    for source, indices in sorted(source_indices.items()):
        print(f"  - {source}: {len(indices):,} 条数据")

    return all_data, source_indices


def load_previous_sampling_records_fast(record_files: List[str]) -> Dict[str, Set[int]]:
    """
    快速加载之前的采样记录，优化大记录文件的加载性能
    """
    sampled_indices = defaultdict(set)

    if not record_files:
        return sampled_indices

    print("正在加载历史采样记录...")
    progress = ProgressTracker(len(record_files), "加载记录")

    for record_file in record_files:
        if not os.path.exists(record_file):
            print(f"\n警告: 记录文件 {record_file} 不存在，跳过")
            progress.update(1)
            continue

        try:
            # 使用更大的缓冲区读取大记录文件
            with open(record_file, 'rb') as f:
                record_data = orjson.loads(f.read())

            if 'source_details' in record_data:
                for source, indices in record_data['source_details'].items():
                    # 批量更新，避免频繁的集合操作
                    sampled_indices[source].update(indices)

            progress.update(1)

        except (orjson.JSONDecodeError, KeyError) as e:
            print(f"\n警告: 记录文件 {record_file} 格式错误: {e}")
            progress.update(1)
            continue

    if sampled_indices:
        total_previous = sum(len(indices) for indices in sampled_indices.values())
        print(f"加载了 {total_previous:,} 条历史采样记录")
        for source, indices in sorted(sampled_indices.items()):
            print(f"  - {source}: {len(indices):,} 条已采样数据")

    return sampled_indices


def get_remaining_indices_fast(source_indices: Dict[str, List[int]],
                               previous_sampled: Dict[str, Set[int]]) -> Dict[str, List[int]]:
    """
    快速计算每个source中未被采样过的索引
    """
    remaining_indices = {}

    print("\n计算剩余可采样数据:")

    # 批量处理，减少循环开销
    for source, all_indices in source_indices.items():
        sampled_set = previous_sampled.get(source, set())

        if not sampled_set:
            # 如果没有历史采样，直接复制列表
            remaining = all_indices.copy()
        else:
            # 使用列表推导式，比逐个检查更快
            remaining = [idx for idx in all_indices if idx not in sampled_set]

        remaining_indices[source] = remaining

        print(f"  - {source}: {len(all_indices):,} 总数据, {len(sampled_set):,} 已采样, {len(remaining):,} 剩余")

    return remaining_indices


def sample_data_fast(all_data: List[dict], remaining_indices: Dict[str, List[int]],
                     sample_ratio: float, random_seed: int = 42) -> Tuple[List[dict], Dict[str, List[int]]]:
    """
    快速采样，优化大数据量的采样性能
    """
    if not 0 < sample_ratio <= 1.0:
        raise ValueError("抽样比例必须在 0 和 1 之间")

    random.seed(random_seed)

    sampled_data = []
    sampled_indices = {}

    print(f"\n开始从剩余数据中抽样 (比例: {sample_ratio * 100:.1f}%):")

    # 预计算总采样量，用于进度显示
    total_to_sample = 0
    for source, indices in remaining_indices.items():
        if indices:
            sample_count = max(1, int(len(indices) * sample_ratio))
            sample_count = min(sample_count, len(indices))
            total_to_sample += sample_count

    if total_to_sample == 0:
        print("没有数据可以采样")
        return [], {}

    progress = ProgressTracker(total_to_sample, "采样数据")

    for source, indices in remaining_indices.items():
        if not indices:
            print(f"  - {source}: 无剩余数据可采样")
            sampled_indices[source] = []
            continue

        # 计算该source需要抽样的数量
        sample_count = max(1, int(len(indices) * sample_ratio))
        sample_count = min(sample_count, len(indices))

        if sample_count == 0:
            sampled_indices[source] = []
            continue

        # 随机抽样 - 使用random.sample比手动随机更高效
        sampled_source_indices = random.sample(indices, sample_count)
        sampled_source_indices.sort()  # 保持索引有序，便于后续处理

        # 批量添加抽样数据
        batch_data = [all_data[idx] for idx in sampled_source_indices]
        sampled_data.extend(batch_data)

        sampled_indices[source] = sampled_source_indices
        print(f"\n  - {source}: 从 {len(indices):,} 剩余数据中采样 {sample_count:,} 条")

        progress.update(sample_count)

    print(f"\n本次抽样完成，共 {len(sampled_data):,} 条数据")
    return sampled_data, sampled_indices


def split_data_into_files_fast(sampled_data: List[dict], output_prefix: str,
                               max_lines_per_file: int = 10000) -> List[str]:
    """
    快速分割数据到多个文件，使用缓冲写入提升性能
    """
    if not sampled_data:
        print("没有数据需要保存")
        return []

    num_files = math.ceil(len(sampled_data) / max_lines_per_file)
    output_files = []

    print(f"\n将数据分割为 {num_files} 个文件 (每文件最多 {max_lines_per_file:,} 行):")

    progress = ProgressTracker(len(sampled_data), "写入文件")

    for file_idx in range(num_files):
        start_idx = file_idx * max_lines_per_file
        end_idx = min(start_idx + max_lines_per_file, len(sampled_data))

        # 生成文件名
        if num_files == 1:
            output_file = f"{output_prefix}.jsonl"
        else:
            output_file = f"{output_prefix}_part{file_idx + 1:03d}.jsonl"

        # 批量写入数据，使用大缓冲区
        with open(output_file, 'w', encoding='utf-8', buffering=8192 * 16) as f:
            # 批量构建字符串，减少IO次数
            lines = []
            batch_size = 1000  # 每1000行批量写入一次

            for i in range(start_idx, end_idx):
                lines.append(orjson.dumps(sampled_data[i]).decode('utf-8'))

                if len(lines) >= batch_size:
                    f.write('\n'.join(lines) + '\n')
                    progress.update(len(lines))
                    lines = []

            # 写入剩余数据
            if lines:
                f.write('\n'.join(lines) + '\n')
                progress.update(len(lines))

        output_files.append(output_file)
        print(f"\n  - {output_file}: {end_idx - start_idx:,} 条数据")

    return output_files


def save_indices_record(sampled_indices: Dict[str, List[int]], record_file: str,
                        sample_ratio: float, random_seed: int, output_files: List[str]):
    """
    保存抽样索引记录，使用优化的JSON写入
    """
    record_data = {
        'timestamp': datetime.now().isoformat(),
        'sample_ratio': sample_ratio,
        'random_seed': random_seed,
        'total_sampled': sum(len(indices) for indices in sampled_indices.values()),
        'output_files': output_files,
        'source_details': sampled_indices
    }

    # 使用大缓冲区写入记录文件
    with open(record_file, 'wb') as f:
        f.write(orjson.dumps(record_data, option=orjson.OPT_INDENT_2))
    print(f"抽样索引记录已保存到: {record_file}")


def main():
    parser = argparse.ArgumentParser(description='高性能JSONL文件多次采样工具')
    parser.add_argument('--input', '-i', default='*.jsonl',
                        help='输入文件匹配模式 (默认: *.jsonl)')
    parser.add_argument('--output', '-o', default='sampled_data',
                        help='输出文件前缀 (默认: sampled_data)')
    parser.add_argument('--ratio', '-r', type=float, default=0.1,
                        help='抽样比例 (默认: 0.1 即10%%)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                        help='随机种子 (默认: 42)')
    parser.add_argument('--record', default='sampling_record.json',
                        help='本次索引记录文件名 (默认: sampling_record.json)')
    parser.add_argument('--previous-records', nargs='*', default=[],
                        help='之前的采样记录文件路径列表')
    parser.add_argument('--max-lines', type=int, default=10000,
                        help='每个输出文件的最大行数 (默认: 10,000)')
    parser.add_argument('--max-workers', type=int, default=4,
                        help='并行读取的最大线程数 (默认: 4)')

    args = parser.parse_args()

    # 验证参数
    if not 0 < args.ratio <= 1.0:
        print("错误: 抽样比例必须在 0 和 1 之间")
        return

    if args.max_lines < 1:
        print("错误: 每个文件的最大行数必须大于0")
        return

    if args.max_workers < 1:
        print("错误: 线程数必须大于0")
        return

    start_time = time.time()

    try:
        # 读取数据
        print("=" * 50)
        print("第一步: 并行读取原始数据")
        print("=" * 50)
        all_data, source_indices = read_jsonl_files_parallel(args.input, args.max_workers)

        if not all_data:
            print("错误: 没有找到有效的数据")
            return

        # 加载之前的采样记录
        print("\n" + "=" * 50)
        print("第二步: 加载历史采样记录")
        print("=" * 50)
        previous_sampled = load_previous_sampling_records_fast(args.previous_records)

        # 计算剩余可采样数据
        print("\n" + "=" * 50)
        print("第三步: 计算剩余可采样数据")
        print("=" * 50)
        remaining_indices = get_remaining_indices_fast(source_indices, previous_sampled)

        total_remaining = sum(len(indices) for indices in remaining_indices.values())
        if total_remaining == 0:
            print("警告: 没有剩余数据可以采样")
            return

        # 从剩余数据中抽样
        print("\n" + "=" * 50)
        print("第四步: 高速采样")
        print("=" * 50)
        sampled_data, sampled_indices = sample_data_fast(
            all_data, remaining_indices, args.ratio, args.seed
        )

        if not sampled_data:
            print("没有采样到任何数据")
            return

        # 保存结果到多个文件
        print("\n" + "=" * 50)
        print("第五步: 快速保存结果")
        print("=" * 50)
        output_files = split_data_into_files_fast(sampled_data, args.output, args.max_lines)

        # 保存记录
        save_indices_record(sampled_indices, args.record, args.ratio, args.seed, output_files)

        # 输出统计信息
        total_time = time.time() - start_time
        print("\n" + "=" * 50)
        print("采样完成统计")
        print("=" * 50)
        print(f"总处理时间: {total_time:.1f}秒")
        print(f"数据处理速度: {len(all_data) / total_time:.0f} 条/秒")
        print(f"原始数据总数: {len(all_data):,} 条")
        print(f"之前已采样: {sum(len(indices) for indices in previous_sampled.values()):,} 条")
        print(f"剩余可采样: {total_remaining:,} 条")
        print(f"本次采样数: {len(sampled_data):,} 条")
        print(f"本次采样比例: {len(sampled_data) / total_remaining * 100:.2f}%")
        print(
            f"累计采样比例: {(sum(len(indices) for indices in previous_sampled.values()) + len(sampled_data)) / len(all_data) * 100:.2f}%")
        print(f"输出文件数: {len(output_files)} 个")
        print(f"平均每文件: {len(sampled_data) // len(output_files):,} 条数据" if output_files else "")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

# 使用示例:
# 
# 1. 高性能首次采样 (抽取10%，使用8线程)
#    python sampler.py -r 0.1 -o "batch1" --record "batch1_record.json" --max-workers 8
# 
# 2. 第二次采样 (从剩余数据中再抽取10%，大文件分割)
#    python sampler.py -r 0.1 -o "batch2" --record "batch2_record.json" --previous-records "batch1_record.json" --max-lines 50000
# 
# 3. 多次历史记录的采样
#    python sampler.py -r 0.05 -o "batch4" --record "batch4_record.json" --previous-records "batch1_record.json" "batch2_record.json" "batch3_record.json"
# 
# 4. 适合大数据量的配置
#    python sampler.py -i "data/**/*.jsonl" -r 0.01 -o "huge_sample" --max-workers 16 --max-lines 100000
# 
# 5. 性能监控模式
#    time python sampler.py -r 0.1 --max-workers 8
#
# 性能优化说明：
# - 使用多线程并行读取文件
# - 增大文件读写缓冲区 (16*8192 bytes)
# - 批量处理数据，减少函数调用开销
# - 实时进度显示，限制更新频率避免性能影响
# - 优化的随机采样算法
# - 批量JSON写入，减少IO次数
# - 内存友好的数据结构操作